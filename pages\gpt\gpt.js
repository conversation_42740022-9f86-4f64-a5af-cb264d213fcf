const request = require('../../utils/request');
const api = require('../../utils/API')

Page({
  data: {
    messages: [],
    inputValue: '',
    question: '',
    scrollTop: 0
  },

  handleInput(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  sendMessage() {
    if (!this.data.inputValue.trim()) {
      return;
    }

    const userMessage = {
      type: 'user',
      content: this.data.inputValue
    };

    this.setData({
      messages: this.data.messages.concat(userMessage),
      inputValue: ''
    }, () => {
      this.scrollToBottom();

      // 调用AI接口获取回复
      this.mockAIResponse(userMessage.content).then((response) => {
        const aiMessage = {
          type: 'ai',
          content: response
        };

        this.setData({
          messages: this.data.messages.concat(aiMessage)
        }, () => {
          this.scrollToBottom();
        });
      }).catch((error) => {
        console.error('AI回复失败:', error);
        const errorMessage = {
          type: 'ai',
          content: '抱歉，我现在无法回复您的问题，请稍后再试。'
        };

        this.setData({
          messages: this.data.messages.concat(errorMessage)
        }, () => {
          this.scrollToBottom();
        });
      });
    });
  },

  mockAIResponse(question) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        this.realAIResponse(question).then((response) => {
          resolve(response);
        }).catch((error) => {
          reject(error);
        });
      }, 1000);
    });
  },

  realAIResponse(question) {
    console.log('question', question)
    const url = api.AskGPT();
    const data = {
      "question": `qustion=${encodeURIComponent(question)}`
    };
    const header = {
      "content-type": 'application/x-www-form-urlencoded',
      "Authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJvcGVuSWQiOiJvSzFlcTY4aUFnTUQyR1NXOGtkZF9rU0lVc3lvIiwiZXhwIjoxNzQ1NDc0NzAxfQ.xCTM8u0Vz_Im7WFBV5Yow2ykbuomS69N3KR_hW-wa4z87HGIXc-6mlBZw5HTF2Bk9e4cf9vdoZRw3LORE7gIQA"
    };

    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'POST',
        header: header,
        data: data,
        success: (res) => {
          console.log('success in ai', res);
          if (res.data && res.data.data) {
            resolve(res.data.data);
          } else {
            resolve('抱歉，我现在无法理解您的问题，请换个方式问我吧。');
          }
        },
        fail: (err) => {
          console.error('调用AI接口出错:', err);
          reject(err);
        }
      });
    });
  },

  scrollToBottom() {
    // 使用 setTimeout 确保DOM更新完成后再滚动
    setTimeout(() => {
      wx.createSelectorQuery().select('.message-scroll-view').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            scrollTop: rect.height + 1000 // 确保滚动到底部
          });
        }
      }).exec();
    }, 100);
  },

  onLoad() {
    // 页面加载时的初始化
    console.log('GPT页面加载');
  },

  onShow() {
    // 页面显示时的处理
    this.scrollToBottom();
  }
});