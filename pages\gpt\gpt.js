const request = require('../../utils/request');
const api = require('../../utils/API')

Page({
  data: {
    messages: [],
    inputValue: '',
    question: '',
    scrollTop: 0,
    isAiTyping: false,
    inputFocus: false
  },

  handleInput(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  sendMessage() {
    if (!this.data.inputValue.trim()) {
      return;
    }

    const userMessage = {
      type: 'user',
      content: this.data.inputValue
    };

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    this.setData({
      messages: this.data.messages.concat(userMessage),
      inputValue: '',
      isAiTyping: true
    }, () => {
      this.scrollToBottom();

      // 调用AI接口获取回复
      this.mockAIResponse(userMessage.content).then((response) => {
        const aiMessage = {
          type: 'ai',
          content: response
        };

        this.setData({
          messages: this.data.messages.concat(aiMessage),
          isAiTyping: false
        }, () => {
          this.scrollToBottom();
        });
      }).catch((error) => {
        console.error('AI回复失败:', error);
        const errorMessage = {
          type: 'ai',
          content: '抱歉，我现在无法回复您的问题，请稍后再试。'
        };

        this.setData({
          messages: this.data.messages.concat(errorMessage),
          isAiTyping: false
        }, () => {
          this.scrollToBottom();
        });
      });
    });
  },

  mockAIResponse(question) {
    return new Promise((resolve, reject) => {
      // 模拟AI思考时间，增加真实感
      const thinkingTime = Math.random() * 2000 + 1000; // 1-3秒
      setTimeout(() => {
        this.realAIResponse(question).then((response) => {
          resolve(response);
        }).catch((error) => {
          reject(error);
        });
      }, thinkingTime);
    });
  },

  realAIResponse(question) {
    console.log('question', question)
    const url = api.AskGPT();
    const data = {
      "question": `qustion=${encodeURIComponent(question)}`
    };
    const header = {
      "content-type": 'application/x-www-form-urlencoded',
      "Authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJvcGVuSWQiOiJvSzFlcTY4aUFnTUQyR1NXOGtkZF9rU0lVc3lvIiwiZXhwIjoxNzQ1NDc0NzAxfQ.xCTM8u0Vz_Im7WFBV5Yow2ykbuomS69N3KR_hW-wa4z87HGIXc-6mlBZw5HTF2Bk9e4cf9vdoZRw3LORE7gIQA"
    };

    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'POST',
        header: header,
        data: data,
        success: (res) => {
          console.log('success in ai', res);
          if (res.data && res.data.data) {
            resolve(res.data.data);
          } else {
            resolve('抱歉，我现在无法理解您的问题，请换个方式问我吧。');
          }
        },
        fail: (err) => {
          console.error('调用AI接口出错:', err);
          reject(err);
        }
      });
    });
  },

  scrollToBottom() {
    // 使用 setTimeout 确保DOM更新完成后再滚动
    setTimeout(() => {
      wx.createSelectorQuery().select('.message-scroll-view').boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            scrollTop: rect.height + 1000 // 确保滚动到底部
          });
        }
      }).exec();
    }, 100);
  },

  // 页面生命周期
  onLoad(options) {
    console.log('GPT页面加载');

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: 'AI智能助手'
    });

    // 预加载一些欢迎消息（可选）
    if (options && options.welcome) {
      this.setData({
        messages: [{
          type: 'ai',
          content: '您好！我是您的AI助手，很高兴为您服务。请问有什么可以帮助您的吗？'
        }]
      });
    }
  },

  onShow() {
    // 页面显示时的处理
    this.scrollToBottom();

    // 设置状态栏样式
    wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    });
  },

  onHide() {
    // 页面隐藏时清理
    this.setData({
      isAiTyping: false
    });
  },

  onUnload() {
    // 页面卸载时的清理工作
    console.log('GPT页面卸载');
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 可以添加清空聊天记录的功能
    wx.showModal({
      title: '清空聊天',
      content: '是否要清空当前聊天记录？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            messages: [],
            isAiTyping: false
          });
        }
        wx.stopPullDownRefresh();
      }
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'AI智能助手 - 您的专属AI伙伴',
      path: '/pages/gpt/gpt',
      imageUrl: '/img/share-ai.png' // 需要添加分享图片
    };
  }
});