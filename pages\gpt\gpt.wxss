/* pages/gpt/gpt.wxss */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
  padding: 0;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 页面容器 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.page-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50rpx 50rpx;
  animation: float 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 欢迎界面容器 */
.welcome-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  position: relative;
  z-index: 1;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx;
  padding: 80rpx 60rpx;
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 8rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  animation: welcomeIn 0.8s ease-out;
}

@keyframes welcomeIn {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* AI图标容器 */
.ai-icon-container {
  margin-bottom: 60rpx;
  position: relative;
}

.ai-icon {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 20rpx 40rpx rgba(102, 126, 234, 0.4),
    0 8rpx 16rpx rgba(102, 126, 234, 0.2);
  position: relative;
  animation: pulse 3s ease-in-out infinite;
}

.ai-icon::before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  opacity: 0.3;
  animation: ripple 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 20rpx 40rpx rgba(102, 126, 234, 0.4),
      0 8rpx 16rpx rgba(102, 126, 234, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow:
      0 25rpx 50rpx rgba(102, 126, 234, 0.5),
      0 10rpx 20rpx rgba(102, 126, 234, 0.3);
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.ai-text {
  font-size: 56rpx;
  font-weight: 700;
  color: white;
  letter-spacing: 4rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

/* 欢迎标题 */
.welcome-title {
  font-size: 52rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 32rpx;
  color: #718096;
  line-height: 1.6;
  opacity: 0.8;
}

/* 聊天容器 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.message-scroll-view {
  flex: 1;
  padding: 30rpx 20rpx;
  padding-bottom: 140rpx;
}

/* 消息样式 */
.message {
  margin-bottom: 40rpx;
  animation: messageSlideIn 0.4s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* AI消息容器 */
.ai-message-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
}

.ai-avatar-small {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  animation: avatarBounce 0.6s ease-out;
}

@keyframes avatarBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.ai-text-small {
  font-size: 26rpx;
  font-weight: 700;
  color: white;
  letter-spacing: 1rpx;
}

.ai-message-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 32rpx;
  border-radius: 24rpx 24rpx 24rpx 8rpx;
  max-width: 520rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  animation: bubbleIn 0.5s ease-out 0.2s both;
}

@keyframes bubbleIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
}

.ai-message-content::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 24rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-right: 12rpx solid rgba(255, 255, 255, 0.95);
}

.ai-message-content text {
  color: #2d3748;
  font-size: 30rpx;
  line-height: 1.6;
}

/* 用户消息容器 */
.user-message-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 40rpx;
}

.user-message-content {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 32rpx;
  border-radius: 24rpx 24rpx 8rpx 24rpx;
  max-width: 520rpx;
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.4),
    0 4rpx 12rpx rgba(102, 126, 234, 0.2);
  position: relative;
  animation: bubbleInRight 0.5s ease-out both;
}

@keyframes bubbleInRight {
  from {
    opacity: 0;
    transform: scale(0.8) translateX(20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
}

.user-message-content::after {
  content: '';
  position: absolute;
  right: -12rpx;
  top: 24rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-left: 12rpx solid #667eea;
}

.user-message-content text {
  font-size: 30rpx;
  line-height: 1.6;
  color: white;
}

/* 输入容器 */
.input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 24rpx 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid rgba(255, 255, 255, 0.3);
  z-index: 1000;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 60rpx;
  padding: 12rpx 24rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow:
    0 8rpx 32rpx rgba(102, 126, 234, 0.2),
    0 4rpx 12rpx rgba(102, 126, 234, 0.1);
  transform: translateY(-2rpx);
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  font-size: 32rpx;
  padding: 24rpx 0;
  background-color: transparent;
  color: #2d3748;
  line-height: 1.4;
}

.input-field::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

.send-button {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  box-shadow:
    0 8rpx 24rpx rgba(102, 126, 234, 0.4),
    0 4rpx 8rpx rgba(102, 126, 234, 0.2);
  transition: all 0.2s ease;
  animation: sendButtonIn 0.3s ease-out;
}

@keyframes sendButtonIn {
  from {
    opacity: 0;
    transform: scale(0.5) rotate(-90deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

.send-button:active {
  transform: scale(0.95);
  box-shadow:
    0 4rpx 12rpx rgba(102, 126, 234, 0.3),
    0 2rpx 4rpx rgba(102, 126, 234, 0.1);
}

.send-icon {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  transform: translateX(2rpx);
}

.send-button-hover {
  transform: scale(0.95) !important;
  box-shadow:
    0 4rpx 12rpx rgba(102, 126, 234, 0.3),
    0 2rpx 4rpx rgba(102, 126, 234, 0.1) !important;
}

.send-button-disabled {
  background: linear-gradient(135deg, #cbd5e0, #a0aec0) !important;
  box-shadow:
    0 4rpx 12rpx rgba(160, 174, 192, 0.2),
    0 2rpx 4rpx rgba(160, 174, 192, 0.1) !important;
  cursor: not-allowed;
}

.send-button-disabled .loading-dots {
  gap: 4rpx;
}

.send-button-disabled .loading-dot {
  width: 8rpx;
  height: 8rpx;
  background: white;
}

/* 输入框禁用状态 */
.input-field:disabled {
  color: #a0aec0;
  cursor: not-allowed;
}

/* 消息气泡悬停效果 */
.ai-message-content:hover,
.user-message-content:hover {
  transform: translateY(-2rpx);
  transition: transform 0.2s ease;
}

/* 头像旋转动画 */
.ai-avatar-small:active {
  animation: avatarSpin 0.5s ease-in-out;
}

@keyframes avatarSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 欢迎界面的额外装饰 */
.welcome-content::before {
  content: '';
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  z-index: -1;
  animation: welcomeGlow 4s ease-in-out infinite;
}

@keyframes welcomeGlow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* 消息时间戳（可选功能） */
.message-timestamp {
  font-size: 24rpx;
  color: #a0aec0;
  text-align: center;
  margin: 20rpx 0;
  opacity: 0.7;
}

/* 快捷回复按钮（可选功能） */
.quick-replies {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.quick-reply-btn {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 30rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #667eea;
  transition: all 0.2s ease;
}

.quick-reply-btn:active {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(0.95);
}

/* 加载状态 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 0;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #a0aec0;
  animation: loadingBounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes loadingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .ai-message-content,
  .user-message-content {
    max-width: 480rpx;
  }

  .welcome-title {
    font-size: 48rpx;
  }

  .welcome-subtitle {
    font-size: 28rpx;
  }

  .ai-icon {
    width: 160rpx;
    height: 160rpx;
  }

  .ai-text {
    font-size: 48rpx;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .welcome-content {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .welcome-title {
    color: #f7fafc;
  }

  .welcome-subtitle {
    color: #e2e8f0;
  }

  .ai-message-content {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .ai-message-content text {
    color: #f7fafc;
  }

  .input-container {
    background: rgba(45, 55, 72, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .input-wrapper {
    background: rgba(74, 85, 104, 0.9);
    border-color: rgba(102, 126, 234, 0.3);
  }

  .input-field {
    color: #f7fafc;
  }

  .input-field::placeholder {
    color: #a0aec0;
  }
}