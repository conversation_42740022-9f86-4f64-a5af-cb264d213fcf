/* pages/gpt/gpt.wxss */
page {
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
  height: 100vh;
}

/* 页面容器 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 欢迎界面容器 */
.welcome-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: #f5f5f5;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* AI图标容器 */
.ai-icon-container {
  margin-bottom: 60rpx;
}

.ai-icon {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #87CEEB, #B0E0E6);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(135, 206, 235, 0.3);
}

.ai-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  letter-spacing: 2rpx;
}

/* 欢迎标题 */
.welcome-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.welcome-subtitle {
  font-size: 32rpx;
  color: #666;
  line-height: 1.5;
}

/* 聊天容器 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.message-scroll-view {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 消息样式 */
.message {
  margin-bottom: 30rpx;
}

/* AI消息容器 */
.ai-message-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.ai-avatar-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #87CEEB, #B0E0E6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.ai-text-small {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.ai-message-content {
  background-color: white;
  padding: 24rpx;
  border-radius: 20rpx;
  max-width: 520rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.ai-message-content::before {
  content: '';
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-right: 12rpx solid white;
}

/* 用户消息容器 */
.user-message-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 30rpx;
}

.user-message-content {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: white;
  padding: 24rpx;
  border-radius: 20rpx;
  max-width: 520rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
  position: relative;
}

.user-message-content::after {
  content: '';
  position: absolute;
  right: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border-top: 12rpx solid transparent;
  border-bottom: 12rpx solid transparent;
  border-left: 12rpx solid #007AFF;
}

/* 输入容器 */
.input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f5f5f5;
  padding: 20rpx;
  border-top: 1rpx solid #e5e5e5;
  z-index: 1000;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 50rpx;
  padding: 8rpx 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e5e5e5;
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  font-size: 32rpx;
  padding: 20rpx 0;
  background-color: transparent;
  color: #333;
}

.input-field::placeholder {
  color: #999;
}

.send-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.send-button:active {
  transform: scale(0.95);
}

.send-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.send-button-hover {
  transform: scale(0.95) !important;
  box-shadow:
    0 4rpx 12rpx rgba(102, 126, 234, 0.3),
    0 2rpx 4rpx rgba(102, 126, 234, 0.1) !important;
}

.send-button-disabled {
  background: linear-gradient(135deg, #cbd5e0, #a0aec0) !important;
  box-shadow:
    0 4rpx 12rpx rgba(160, 174, 192, 0.2),
    0 2rpx 4rpx rgba(160, 174, 192, 0.1) !important;
  cursor: not-allowed;
}

.send-button-disabled .loading-dots {
  gap: 4rpx;
}

.send-button-disabled .loading-dot {
  width: 8rpx;
  height: 8rpx;
  background: white;
}



/* 加载状态 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 0;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #999;
  animation: loadingBounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes loadingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .ai-message-content,
  .user-message-content {
    max-width: 480rpx;
  }

  .welcome-title {
    font-size: 48rpx;
  }

  .welcome-subtitle {
    font-size: 28rpx;
  }

  .ai-icon {
    width: 160rpx;
    height: 160rpx;
  }

  .ai-text {
    font-size: 48rpx;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .welcome-content {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .welcome-title {
    color: #f7fafc;
  }

  .welcome-subtitle {
    color: #e2e8f0;
  }

  .ai-message-content {
    background: rgba(45, 55, 72, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .ai-message-content text {
    color: #f7fafc;
  }

  .input-container {
    background: rgba(45, 55, 72, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .input-wrapper {
    background: rgba(74, 85, 104, 0.9);
    border-color: rgba(102, 126, 234, 0.3);
  }

  .input-field {
    color: #f7fafc;
  }

  .input-field::placeholder {
    color: #a0aec0;
  }
}