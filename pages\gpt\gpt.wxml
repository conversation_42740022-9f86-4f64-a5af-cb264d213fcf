<!--pages/gpt/gpt.wxml-->
<view class="page-container">
  <!-- 欢迎界面 -->
  <view wx:if="{{messages.length === 0}}" class="welcome-container">
    <view class="welcome-content">
      <view class="ai-icon-container">
        <view class="ai-icon">
          <text class="ai-text">AI</text>
        </view>
      </view>
      <view class="welcome-title">欢迎使用AI助手</view>
      <view class="welcome-subtitle">有什么我可以帮您的吗？</view>
    </view>
  </view>

  <!-- 聊天界面 -->
  <view wx:else class="chat-container">
    <scroll-view scroll-y="{{true}}" class="message-scroll-view" scroll-top="{{scrollTop}}">
      <view class="message {{item.type === 'user'? 'user-message' : 'ai-message'}}" wx:for="{{messages}}" wx:key="index">
        <view wx:if="{{item.type === 'ai'}}" class="ai-message-container">
          <view class="ai-avatar-small">
            <text class="ai-text-small">AI</text>
          </view>
          <view class="ai-message-content">
            <text>{{item.content}}</text>
          </view>
        </view>
        <view wx:if="{{item.type === 'user'}}" class="user-message-container">
          <view class="user-message-content">
            <text>{{item.content}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 输入框 -->
  <view class="input-container">
    <view class="input-wrapper">
      <input
        type="text"
        placeholder="输入您的问题..."
        class="input-field"
        value="{{inputValue}}"
        bindinput="handleInput"
        bindconfirm="sendMessage"
        confirm-type="send"
      />
      <view wx:if="{{inputValue.trim()}}" class="send-button" bindtap="sendMessage">
        <text class="send-icon">→</text>
      </view>
    </view>
  </view>
</view>